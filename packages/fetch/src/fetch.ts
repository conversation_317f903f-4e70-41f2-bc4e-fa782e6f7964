import { RequestOptions } from "@continuedev/config-types";
import { RequestInit, Response } from "node-fetch";
import fetchWrapper from "./fetch-wrapper.js";

export async function fetchwithRequestOptions(
  url_: URL | string,
  init?: RequestInit,
  requestOptions?: RequestOptions,
): Promise<Response> {
  try {
    return fetchWrapper(url_, init, requestOptions);
  } catch (e) {
    return new Response(null, {
      status: 499, // Client Closed Request
      statusText: "Client Closed Request",
    });
  }
}

export async function fetchCompletionsWithRequestOptions(
  url_: URL | string,
  init?: RequestInit,
  requestOptions?: RequestOptions,
): Promise<Response> {
  try {
    return fetchWrapper(url_, init, requestOptions);
  } catch (e) {
    return new Response(null, {
      status: 499, // Client Closed Request
      statusText: "Client Closed Request",
    });
  }
}
