import { RequestOptions } from "@continuedev/config-types";
import { BodyInit, RequestInit, Response } from "node-fetch";
import { Pool } from "undici";
import { <PERSON><PERSON><PERSON> } from "buffer";

// Create a global pool map to manage connection pools
const poolMap = new Map<string, Pool>();

function getPool(origin: string): Pool {
  if (!poolMap.has(origin)) {
    // Create a new pool with HTTP/2 enabled
    const pool = new Pool(origin, {
      allowH2: true,
      connections: 100, // Max connections in the pool
      keepAliveMaxTimeout: 600000, // 10 minutes
      keepAliveTimeoutThreshold: 1000, // 1 second
      pipelining: 1, // HTTP/2 pipelining
      connect: {
        allowH2: true, // Enable HTTP/2
      },
    });
    poolMap.set(origin, pool);
  }
  return poolMap.get(origin)!;
}

async function fetchWrapper(
  url_: URL | string,
  init?: RequestInit,
  requestOptions?: RequestOptions,
): Promise<Response> {
  const url = typeof url_ === "string" ? new URL(url_) : url_;
  const shouldLog = url.pathname?.endsWith("completions");

  // Replace localhost with 127.0.0.1
  if (url.hostname === "localhost") {
    url.hostname = "127.0.0.1";
  }

  // Don't use proxy for undici, handle it differently
  let headers: { [key: string]: string } = {};
  for (const [key, value] of Object.entries(init?.headers || {})) {
    headers[key] = value as string;
  }
  headers = {
    ...headers,
    ...requestOptions?.headers,
  };

  // Add extra body properties if provided
  let updatedBody: string | undefined = undefined;
  try {
    if (requestOptions?.extraBodyProperties && typeof init?.body === "string") {
      const parsedBody = JSON.parse(init.body);
      updatedBody = JSON.stringify({
        ...parsedBody,
        ...requestOptions.extraBodyProperties,
      });
    }
  } catch (e) {
    console.debug("Unable to parse HTTP request body: ", e);
  }

  const finalBody = updatedBody ?? init?.body;
  const method = init?.method || "GET";

  // Verbose logging for debugging - log request details
  if (process.env.VERBOSE_FETCH) {
    logRequest(method, url, headers, finalBody);
  }

  // Create origin for pool
  const origin = `${url.protocol}//${url.host}`;

  // Get or create pool for this origin
  const pool = getPool(origin);

  // Prepare request options for undici
  const undiciOptions: any = {
    path: url.pathname + url.search,
    method,
    headers,
    body: finalBody as string | Buffer | null,
    signal: init?.signal,
  };

  try {
    // Use undici pool to make the request
    const resp = await pool.request(undiciOptions);

    // Convert undici response to node-fetch Response format
    // Create headers object compatible with node-fetch
    const responseHeaders: [string, string][] = [];
    for (const [key, value] of Object.entries(resp.headers)) {
      if (value !== undefined) {
        // Handle both string and string[] values
        if (Array.isArray(value)) {
          // For arrays, add each value as a separate header
          for (const val of value) {
            responseHeaders.push([key, val]);
          }
        } else {
          responseHeaders.push([key, value]);
        }
      }
    }

    // Convert undici body to node-fetch compatible body
    // To avoid issues with code minification in binary packaging, we always convert to Buffer
    // This ensures maximum compatibility regardless of how classes are renamed or removed during packing
    let fetchBody: any = null;
    if (resp.body) {
      try {
        // Always convert the undici body to a Buffer for maximum compatibility
        // This approach works regardless of code minification or binary packaging
        fetchBody = await undiciBodyToBuffer(resp.body);
      } catch (err) {
        console.debug(
          "Failed to convert undici body to Buffer, using null body:",
          err,
        );
        // If all else fails, use null body
        fetchBody = null;
      }
    }

    const response = new Response(fetchBody, {
      status: resp.statusCode,
      headers: Object.fromEntries(responseHeaders),
    });

    // Verbose logging for debugging - log response details
    if (shouldLog || process.env.VERBOSE_FETCH) {
      await logResponse(response);
    }

    if (!response.ok) {
      const requestId = response.headers.get("x-request-id");
      if (requestId) {
        console.debug(`Request ID: ${requestId}, Status: ${response.status}`);
      }
    }

    return response;
  } catch (error) {
    // Verbose logging for errors
    if (shouldLog || process.env.VERBOSE_FETCH) {
      logError(error);
    }
    if (error instanceof Error && error.name === "AbortError") {
      // Return a Response object that streamResponse etc can handle
      return new Response(null, {
        status: 499, // Client Closed Request
        statusText: "Client Closed Request",
      });
    }
    throw error;
  }
}

function logRequest(
  method: string,
  url: URL,
  headers: { [key: string]: string },
  body: BodyInit | null | undefined,
  proxy?: string,
  shouldBypass?: boolean,
) {
  console.debug("=== FETCH REQUEST ===");
  console.debug(`Method: ${method}`);
  console.debug(`URL: ${url.toString()}`);

  // Log headers in curl format
  console.debug("Headers:");
  for (const [key, value] of Object.entries(headers)) {
    console.debug(`  -H '${key}: ${value}'`);
  }

  // Log proxy information
  if (proxy && !shouldBypass) {
    console.debug(`Proxy: ${proxy}`);
  }

  // Log body
  if (body) {
    console.debug(`Body: ${body}`);
  }

  // Generate equivalent curl command
  let curlCommand = `curl -X ${method}`;
  for (const [key, value] of Object.entries(headers)) {
    curlCommand += ` -H '${key}: ${value}'`;
  }
  if (body) {
    curlCommand += ` -d '${body}'`;
  }
  if (proxy && !shouldBypass) {
    curlCommand += ` --proxy '${proxy}'`;
  }
  curlCommand += ` '${url.toString()}'`;
  console.debug(`Equivalent curl: ${curlCommand}`);
  console.debug("=====================");
}

async function logResponse(resp: Response) {
  console.debug("=== FETCH RESPONSE ===");
  console.debug(`Status: ${resp.status} ${resp.statusText}`);
  console.debug("Response Headers:");
  resp.headers.forEach((value, key) => {
    console.debug(`  ${key}: ${value}`);
  });

  // TODO: For streamed responses, this caused the response to be consumed and the connection would just hang open
  // Clone response to read body without consuming it
  // const respClone = resp.clone();
  // try {
  //   const responseText = await respClone.text();
  //   console.debug(`Response Body: ${responseText}`);
  // } catch (e) {
  //   console.debug("Could not read response body:", e);
  // }
  console.debug("======================");
}

function logError(error: unknown) {
  console.debug("=== FETCH ERROR ===");
  console.debug(`Error: ${error}`);
  console.debug("===================");
}

// Helper function to convert undici body to Buffer
async function undiciBodyToBuffer(body: any): Promise<Buffer | null> {
  if (!body) return null;

  const chunks: any[] = [];
  if (typeof body[Symbol.asyncIterator] === "function") {
    for await (const chunk of body) {
      chunks.push(chunk);
    }
    // Use Buffer.concat with type assertion to avoid TS errors
    return Buffer.concat(chunks.map((c) => Buffer.from(c)) as any);
  } else {
    // If it's already a buffer or string, return as Buffer
    if (Buffer.isBuffer(body)) {
      return body;
    } else if (typeof body === "string") {
      return Buffer.from(body);
    }
    return null;
  }
}

export default fetchWrapper;
