<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin>
    <id>com.taobao.mc.aimi</id>
    <name>AIMI</name>
    <vendor url="https://alibaba.com">淘宝</vendor>

    <depends>com.intellij.modules.platform</depends>
    <depends optional="true" config-file="plugin-kotlin.xml">
        org.jetbrains.kotlin
    </depends>
    <depends optional="true" config-file="plugin-java.xml">
        com.intellij.java
    </depends>
    <depends optional="true" config-file="plugin-javascript.xml">
        JavaScript
    </depends>

    <!-- See here for why this is optional:  https://github.com/continuedev/continue/issues/2775#issuecomment-2535620877-->
    <depends optional="true" config-file="plugin-json.xml">
        com.intellij.modules.json
    </depends>

    <extensions defaultExtensionNs="com.intellij">
        <postStartupActivity implementation="com.taobao.mc.aimi.startup.ClipboardMonitorStartupActivity"/>
        <postStartupActivity implementation="com.taobao.mc.aimi.startup.AIMIStartupActivity"/>
    </extensions>

    <extensions defaultExtensionNs="com.intellij">
        <editorFactoryListener
                implementation="com.taobao.mc.aimi.ext.autocomplete.AutocompleteEditorListener"/>
        <toolWindow id="AIMI" anchor="right" icon="com.taobao.mc.aimi.util.AIMIIcons.AIMI"
                    factoryClass="com.taobao.mc.aimi.ext.toolWindow.AIMIPluginToolWindowFactory"/>
        <projectService id="ContinuePluginService"
                        serviceImplementation="com.taobao.mc.aimi.ext.services.AIMIPluginService"/>
        <projectService
                id="DiffStreamService"
                serviceImplementation="com.taobao.mc.aimi.ext.editor.DiffStreamService"/>
        <projectService
                id="AutocompleteLookupListener"
                serviceImplementation="com.taobao.mc.aimi.ext.autocomplete.AutocompleteLookupListener"/>
        <projectService
                id="UIFreezeDetectionService"
                serviceImplementation="com.taobao.mc.aimi.services.UIFreezeDetectionService"/>
        <projectService
                id="ObjectTypeResolver"
                serviceImplementation="com.taobao.mc.aimi.psi.ObjectTypeResolver"/>
        <statusBarWidgetFactory
                implementation="com.taobao.mc.aimi.ext.autocomplete.AutocompleteSpinnerWidgetFactory"
                id="AutocompleteSpinnerWidget"/>
        <notificationGroup id="AIMI"
                           displayType="BALLOON"/>
        <actionPromoter order="last"
                        implementation="com.taobao.mc.aimi.ext.actions.AIMIActionPromote"/>
        <errorHandler implementation="com.taobao.mc.aimi.ext.error.AIMIErrorSubmitter"/>
        <postStartupActivity implementation="com.taobao.mc.aimi.ext.activities.AIMIPluginStartupActivity"/>
        <postStartupActivity implementation="com.taobao.mc.aimi.proxy.ProxyPoolingActivity"/>
        <applicationConfigurable
                parentId="tools"
                instance="com.taobao.mc.aimi.settings.AIMISettingsConfigurable"
                id="com.taobao.mc.aimi.settings.AIMISettingsConfigurable"
                displayName="AIMI"/>
        <applicationService
                serviceImplementation="com.taobao.mc.aimi.settings.AIMISettingService"/>
        <applicationService
                serviceImplementation="com.taobao.mc.aimi.settings.UIFreezeDetectionSettings"/>
    </extensions>

    <actions>
        <!--<action class="com.taobao.mc.aimi.ext.editor.InlineEditAction"
                id="aimi.inlineEdit"
                description="Inline Edit"
                text="Inline Edit">
            <keyboard-shortcut keymap="$default"
                               first-keystroke="ctrl I"/>
            <keyboard-shortcut keymap="Mac OS X"
                               first-keystroke="meta I"/>
            <override-text place="GoToAction" text="AIMI: Edit Code"/>
        </action>-->

        <action id="aimi.acceptDiff"
                class="com.taobao.mc.aimi.ext.actions.AcceptDiffAction"
                text="全部接受"
                description="接受所有的修改">
            <keyboard-shortcut keymap="$default"
                               first-keystroke="shift ctrl ENTER"/>
            <keyboard-shortcut keymap="Mac OS X"
                               first-keystroke="shift meta ENTER"/>
            <override-text place="GoToAction" text="AIMI: Accept Diff"/>
        </action>


        <action id="continue.restartProcess"
                class="com.taobao.mc.aimi.ext.actions.RestartAIMIProcess"
                text="Restart Process"
                description="Restart Process">
            <override-text place="GoToAction" text="AIMI: Restart Process"/>
        </action>

        <action id="continue.rejectDiff"
                class="com.taobao.mc.aimi.ext.actions.RejectDiffAction"
                text="全部拒绝"
                description="拒绝所有的修改">
            <keyboard-shortcut keymap="$default"
                               first-keystroke="shift ctrl BACK_SPACE"/>
            <keyboard-shortcut keymap="Mac OS X"
                               first-keystroke="shift meta BACK_SPACE"/>
            <override-text place="GoToAction" text="AIMI: Reject Diff"/>
        </action>

        <!--<action id="aimi.acceptVerticalDiffBlock"
                class="com.taobao.mc.aimi.ext.actions.AcceptDiffAction"
                text="Accept Diff" description="Accept Vertical Diff Block">
            <keyboard-shortcut keymap="$default"
                               first-keystroke="alt shift Y"/>
            <keyboard-shortcut keymap="Mac OS X"
                               first-keystroke="alt shift Y"/>
            <override-text place="GoToAction" text="AIMI: Accept Vertical Diff Block"/>
        </action>

        <action id="aimi.rejectVerticalDiffBlock"
                class="com.taobao.mc.aimi.ext.actions.RejectDiffAction"
                text="Reject Diff" description="Reject Vertical Diff Block">
            <keyboard-shortcut keymap="$default"
                               first-keystroke="alt shift N"/>
            <keyboard-shortcut keymap="Mac OS X"
                               first-keystroke="alt shift N"/>
            <override-text place="GoToAction" text="AIMI: Reject Vertical Diff Block"/>
        </action>-->

        <action id="aimi.focusInputWithoutClear"
                class="com.taobao.mc.aimi.ext.actions.FocusAIMIInputWithoutClearAction"
                text="添加到当前对话"
                description="将选中代码块发送到当前会话">
            <keyboard-shortcut keymap="$default"
                               first-keystroke="ctrl shift J"/>
            <keyboard-shortcut keymap="Mac OS X"
                               first-keystroke="meta shift J"/>
            <override-text place="GoToAction" text="AIMI: Add Highlighted Code to Context"/>
        </action>

        <action id="aimi.newContinueSession"
                icon="AllIcons.General.Add"
                class="com.taobao.mc.aimi.ext.actions.NewAIMISessionAction"
                text="新会话">

            <override-text place="GoToAction" text="New Session"/>
        </action>

        <!--<action id="aimi.viewHistory"
                icon="AllIcons.Vcs.History"
                class="com.taobao.mc.aimi.ext.actions.ViewHistoryAction"
                text="View History"
                description="View History">
            <override-text place="GoToAction" text="View History"/>
        </action>-->

        <!--<action id="aimi.openConfigPage"
                class="com.taobao.mc.aimi.ext.actions.OpenConfigAction"
                icon="AllIcons.General.GearPlain"
                text="AIMI Config"
                description="AIMI Config">
            <override-text place="GoToAction" text="AIMI Config"/>
        </action>-->

        <!--<action id="aimi.openLogs"
                class="com.taobao.mc.aimi.ext.actions.OpenLogsAction"
                icon="AllIcons.General.ShowInfos"
                text="Open Logs"
                description="Open AIMI Logs">
            <override-text place="GoToAction" text="Open AIMI Logs"/>
        </action>-->

        <action id="aimi.focusInput"
                class="com.taobao.mc.aimi.ext.actions.FocusAIMIInputAction"
                text="添加到对话并优化代码"
                description="自动为光标所在文件或选中代码块做代码优化">
            <keyboard-shortcut keymap="$default"
                               first-keystroke="ctrl J"/>
            <keyboard-shortcut keymap="Mac OS X"
                               first-keystroke="meta J"/>
            <override-text place="GoToAction" text="AIMI: Add Highlighted Code to Context and Clear Chat"/>
        </action>

        <action id="aimi.acceptAutocomplete"
                class="com.taobao.mc.aimi.ext.autocomplete.AcceptAutocompleteAction"
                text="接受代码补全"
                description="在光标所在位置接受代码补全">
            <keyboard-shortcut keymap="$default" first-keystroke="TAB"/>
            <keyboard-shortcut keymap="Mac OS X" first-keystroke="TAB"/>
        </action>

        <action id="aimi.cancelAutocomplete"
                class="com.taobao.mc.aimi.ext.autocomplete.CancelAutocompleteAction"
                text="取消代码补全"
                description="取消当前光标位置的代码补全">
            <keyboard-shortcut keymap="$default" first-keystroke="ESCAPE"/>
            <keyboard-shortcut keymap="Mac OS X" first-keystroke="ESCAPE"/>
        </action>

        <action id="com.taobao.mc.aimi.nextEdit.AcceptNextEditAction"
                class="com.taobao.mc.aimi.nextEdit.AcceptNextEditAction"
                text="Accept Autocomplete Suggestion" description="Accept Next Edit Prediction">
            <keyboard-shortcut keymap="$default" first-keystroke="TAB"/>
            <keyboard-shortcut keymap="Mac OS X" first-keystroke="TAB"/>
        </action>

        <action id="com.taobao.mc.aimi.ext.autocomplete.PartialAcceptAutocompleteAction"
                class="com.taobao.mc.aimi.ext.autocomplete.PartialAcceptAutocompleteAction"
                text="部分接受补全建议">
            <keyboard-shortcut first-keystroke="control alt RIGHT" keymap="$default"/>
            <keyboard-shortcut first-keystroke="alt meta RIGHT" keymap="Mac OS X"/>
        </action>

        <!-- 新增主动触发代码补全的 Action -->
        <action id="aimi.triggerAutocomplete"
                class="com.taobao.mc.aimi.autocomplete.TriggerAutocompleteAction"
                text="触发代码补全"
                description="在光标所在位置触发代码补全">
            <keyboard-shortcut keymap="$default" first-keystroke="ctrl BACK_SLASH"/>
            <keyboard-shortcut keymap="Mac OS X" first-keystroke="meta BACK_SLASH"/>
            <override-text place="GoToAction"/>
        </action>
        <!--新增开关AIMI界面的Action-->
        <action id="aimi.toggleAIMI"
                icon="com.taobao.mc.aimi.util.AIMIIcons.AIMI"
                class="com.taobao.mc.aimi.actions.ToggleAIMIAction"
                text="打开/关闭 AIMI"
                description="在IDE激活时开启/关闭AIMI窗口">
            <keyboard-shortcut keymap="$default" first-keystroke="alt A"/>
            <keyboard-shortcut keymap="Mac OS X" first-keystroke="alt A"/>
            <override-text place="GoToAction"/>
        </action>
        <!--新增开关AIMI设置界面的Action-->
        <action id="aimi.openSettings"
                icon="AllIcons.General.GearPlain"
                class="com.taobao.mc.aimi.ext.autocomplete.OpenSettingsAction"
                text="AIMI设置"
        >
            <override-text place="GoToAction"/>
        </action>
        <!--新增开关AIMI历史会话界面的Action-->
        <action id="aimi.openHistoryWindow"
                icon="AllIcons.Vcs.History"
                class="com.taobao.mc.aimi.actions.OpenHistoryWindowAction"
                text="历史会话"
        >
            <override-text place="GoToAction"/>
        </action>

        <!--新增开关API窗口的Action-->
        <!--<action id="aimi.openAPIWindow"
                icon="AllIcons.Nodes.Interface"
                class="com.taobao.mc.aimi.actions.OpenAPIWindowAction"
                text="API"
        >
            <override-text place="GoToAction"/>
        </action>-->

        <!--新增开关Continue窗口的Action-->
        <!--<action id="aimi.openContinueWindow"
                icon="AllIcons.Actions.Execute"
                class="com.taobao.mc.aimi.actions.OpenContinueWindowAction"
                text="Continue"
        >
            <override-text place="GoToAction"/>
        </action>-->

        <!--新增开发者工具Action-->
        <action id="aimi.openDevTools"
                icon="AllIcons.Toolwindows.ToolWindowDebugger"
                class="com.taobao.mc.aimi.actions.OpenDevToolsAction"
                text="开发者工具"
        >
            <override-text place="GoToAction"/>
        </action>

        <!--新增搜索Action-->
        <!--<action id="aimi.search"
                icon="AllIcons.Actions.Search"
                class="com.taobao.mc.aimi.actions.SearchAction"
                text="搜索"
        >
            <override-text place="GoToAction"/>
        </action>-->

        <!--新增RipGrep搜索Action-->
        <!--<action id="aimi.ripgrepSearch"
                icon="AllIcons.Actions.Find"
                class="com.taobao.mc.aimi.actions.RipGrepSearchAction"
                text="RipGrep搜索"
        >
            <override-text place="GoToAction"/>
        </action>-->

        <!--取消操作-->
        <action
                id="aimi.cancelAll"
                class="com.taobao.mc.aimi.actions.CancelAction"
                text="取消">
        </action>

        <!--刷新页面Action-->
        <action id="aimi.refreshPage"
                icon="AllIcons.Actions.Refresh"
                class="com.taobao.mc.aimi.actions.RefreshPageAction"
                text="刷新页面"
        >
            <override-text place="GoToAction"/>
        </action>

        <group id="aimi.EditorPopupMenu"
               popup="true"
               icon="com.taobao.mc.aimi.util.AIMIIcons.AIMI"
               text="AIMI">
            <reference ref="aimi.focusInput"/>
            <reference ref="aimi.focusInputWithoutClear"/>
            <add-to-group group-id="EditorPopupMenu" anchor="first"/>
        </group>

        <group id="aimi.SidebarActionsGroup">
            <reference ref="aimi.newContinueSession"/>
            <reference ref="aimi.openHistoryWindow"/>
            <reference ref="aimi.openSettings"/>
            <reference ref="aimi.refreshPage"/>
        </group>
    </actions>
</idea-plugin>